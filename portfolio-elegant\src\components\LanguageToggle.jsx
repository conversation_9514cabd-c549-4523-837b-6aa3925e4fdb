import { useLanguage } from '../contexts/LanguageContext';

const LanguageToggle = () => {
  const { language, toggleLanguage } = useLanguage();

  return (
    <button
      onClick={toggleLanguage}
      className="flex items-center space-x-2 px-3 py-2 rounded-lg bg-slate-100 hover:bg-slate-200 transition-colors duration-200"
      title={language === 'vi' ? 'Switch to English' : '<PERSON><PERSON><PERSON><PERSON> sang Tiếng Việt'}
    >
      <span className="text-lg">
        {language === 'vi' ? '🇻🇳' : '🇺🇸'}
      </span>
      <span className="text-sm font-medium text-slate-700">
        {language === 'vi' ? 'VI' : 'EN'}
      </span>
    </button>
  );
};

export default LanguageToggle;
