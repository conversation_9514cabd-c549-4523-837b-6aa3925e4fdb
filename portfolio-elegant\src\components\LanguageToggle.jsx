import { useLanguage } from '../contexts/LanguageContext';

const LanguageToggle = () => {
  const { language, toggleLanguage } = useLanguage();

  return (
    <div className="relative">
      <button
        onClick={toggleLanguage}
        className="group relative flex items-center space-x-2 px-4 py-2 rounded-full bg-white border border-slate-200 hover:border-blue-300 hover:shadow-md transition-all duration-300 overflow-hidden"
        title={language === 'vi' ? 'Switch to English' : '<PERSON><PERSON><PERSON><PERSON> sang Tiếng Việt'}
      >
        {/* Background gradient on hover */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-50 to-indigo-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

        {/* Content */}
        <div className="relative flex items-center space-x-2">
          <div className="flex items-center space-x-1">
            <span className="text-lg transition-transform duration-300 group-hover:scale-110">
              {language === 'vi' ? '🇻🇳' : '🇺🇸'}
            </span>
            <span className="text-sm font-semibold text-slate-700 group-hover:text-blue-600 transition-colors duration-300">
              {language === 'vi' ? 'VI' : 'EN'}
            </span>
          </div>

          {/* Separator */}
          <div className="w-px h-4 bg-slate-300 group-hover:bg-blue-300 transition-colors duration-300"></div>

          {/* Next language preview */}
          <div className="flex items-center space-x-1 opacity-50 group-hover:opacity-100 transition-opacity duration-300">
            <span className="text-sm">
              {language === 'vi' ? '🇺🇸' : '🇻🇳'}
            </span>
            <span className="text-xs text-slate-500 group-hover:text-blue-500 transition-colors duration-300">
              {language === 'vi' ? 'EN' : 'VI'}
            </span>
          </div>
        </div>

        {/* Subtle shine effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 group-hover:opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
      </button>
    </div>
  );
};

export default LanguageToggle;
