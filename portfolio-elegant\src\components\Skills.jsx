import { skills } from '../data/mockData';
import { useLanguage } from '../contexts/LanguageContext';
import { translations } from '../data/translations';

const Skills = () => {
  const { language } = useLanguage();
  const t = translations[language];

  const SkillCard = ({ skill }) => (
    <div className="bg-slate-50 rounded-lg p-4 text-center hover:bg-slate-100 transition-colors duration-200 group">
      <div className="text-3xl mb-3 group-hover:scale-110 transition-transform duration-200">
        {skill.icon}
      </div>
      <h4 className="text-slate-700 font-medium text-sm">
        {skill.name}
      </h4>
    </div>
  );

  return (
    <section id="skills" className="section-padding bg-white">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold text-slate-900 mb-4">
            {t.skills.title}
          </h2>
          <p className="text-lg text-slate-600 max-w-2xl mx-auto">
            {t.skills.subtitle}
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {skills.map((category, categoryIndex) => (
            <div key={categoryIndex} className="card">
              <div className="text-center mb-6">
                <h3 className="text-xl font-semibold text-slate-800 mb-2">
                  {t.skills.categories[category.category.toLowerCase()]}
                </h3>
                <div className="w-16 h-1 bg-gradient-to-r from-blue-500 to-blue-500 mx-auto rounded-full"></div>
              </div>

              <div className="grid grid-cols-2 gap-3">
                {category.technologies.map((skill, skillIndex) => (
                  <SkillCard key={skillIndex} skill={skill} />
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Additional Skills Grid */}
        <div className="mt-16">
          <h3 className="text-2xl font-semibold text-slate-800 text-center mb-8">
            What I Bring to Your Project
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center p-6 rounded-lg bg-gradient-to-br from-blue-50 to-blue-50 hover:shadow-lg transition-shadow">
              <div className="text-3xl mb-3">🎨</div>
              <h4 className="font-semibold text-slate-800 mb-2">UI/UX Design</h4>
              <p className="text-slate-600 text-sm">Clean, modern interfaces that users love</p>
            </div>
            
            <div className="text-center p-6 rounded-lg bg-gradient-to-br from-green-50 to-emerald-50 hover:shadow-lg transition-shadow">
              <div className="text-3xl mb-3">⚡</div>
              <h4 className="font-semibold text-slate-800 mb-2">Performance</h4>
              <p className="text-slate-600 text-sm">Fast, optimized applications</p>
            </div>
            
            <div className="text-center p-6 rounded-lg bg-gradient-to-br from-purple-50 to-pink-50 hover:shadow-lg transition-shadow">
              <div className="text-3xl mb-3">📱</div>
              <h4 className="font-semibold text-slate-800 mb-2">Responsive</h4>
              <p className="text-slate-600 text-sm">Perfect on all devices and screens</p>
            </div>
            
            <div className="text-center p-6 rounded-lg bg-gradient-to-br from-orange-50 to-red-50 hover:shadow-lg transition-shadow">
              <div className="text-3xl mb-3">🔧</div>
              <h4 className="font-semibold text-slate-800 mb-2">Maintenance</h4>
              <p className="text-slate-600 text-sm">Clean code that's easy to maintain</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Skills;
