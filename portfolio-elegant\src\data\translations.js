export const translations = {
  vi: {
    // Navigation
    nav: {
      about: "Giới thiệu",
      projects: "Dự án", 
      skills: "<PERSON><PERSON> năng",
      contact: "<PERSON><PERSON><PERSON> hệ"
    },
    
    // Hero Section
    hero: {
      greeting: "<PERSON><PERSON> chà<PERSON>, tôi là",
      title: "Fullstack Web Developer",
      tagline: "Gi<PERSON><PERSON> các thương hiệu xây dựng ứng dụng web hiện đại và có thể mở rộng",
      viewWork: "Xem Dự Án",
      getInTouch: "Liên Hệ Ngay"
    },
    
    // About Section
    about: {
      title: "Giới Thiệu",
      subtitle: "Đam mê tạo ra những giải pháp số tạo nên sự khác biệt",
      greeting: "Xin chào, tôi là",
      bio: "Tôi là một lập trình viên fullstack 23 tuổi với niềm đam mê tạo ra những ứng dụng web hiện đại và có ý nghĩa. Với 2+ năm kinh nghiệm freelance, tôi đã giúp nhiều doanh nghiệp và cá nhân hiện thực hóa ý tưởng của họ thành những sản phẩm công nghệ chất lượng cao.",
      highlights: {
        quality: {
          title: "Tập Trung Chất Lượng",
          desc: "Tôi luôn đặt chất lượng code và user experience lên hàng đầu trong mọi dự án."
        },
        delivery: {
          title: "Giao Hàng Nhanh",
          desc: "Cam kết giao hàng đúng deadline với chất lượng cao và communication tốt."
        },
        tech: {
          title: "Công Nghệ Hiện Đại",
          desc: "Luôn cập nhật và sử dụng những công nghệ mới nhất để tối ưu hiệu suất."
        },
        collaboration: {
          title: "Hợp Tác Khách Hàng",
          desc: "Làm việc chặt chẽ với khách hàng, lắng nghe feedback và điều chỉnh linh hoạt."
        }
      },
      stats: {
        age: "Tuổi",
        location: "Địa điểm",
        experience: "Kinh nghiệm",
        projects: "Dự án"
      },
      downloadCV: "Tải CV",
      sendEmail: "Gửi Email"
    },
    
    // Projects Section
    projects: {
      title: "Dự Án Nổi Bật",
      subtitle: "Showcase các dự án gần đây và những công nghệ tôi đã thành thạo",
      viewDetails: "Xem Chi Tiết",
      role: "Vai trò:",
      duration: "Thời gian:",
      year: "Năm:",
      keyFeatures: "Tính năng chính:",
      techUsed: "Công nghệ sử dụng:",
      status: {
        completed: "Hoàn thành",
        inProgress: "Đang thực hiện",
        planning: "Đang lên kế hoạch"
      }
    },
    
    // Skills Section
    skills: {
      title: "Công Nghệ & Kỹ Năng",
      subtitle: "Các công nghệ và công cụ tôi sử dụng để biến ý tưởng thành hiện thực",
      categories: {
        frontend: "Frontend",
        backend: "Backend", 
        tools: "Công Cụ & Khác"
      },
      whatIBring: "Những Gì Tôi Mang Đến Cho Dự Án Của Bạn",
      highlights: {
        design: {
          title: "UI/UX Design",
          desc: "Giao diện sạch, hiện đại mà người dùng yêu thích"
        },
        performance: {
          title: "Hiệu Suất",
          desc: "Ứng dụng nhanh, được tối ưu hóa"
        },
        responsive: {
          title: "Responsive",
          desc: "Hoàn hảo trên mọi thiết bị và màn hình"
        },
        maintenance: {
          title: "Bảo Trì",
          desc: "Code sạch, dễ bảo trì"
        }
      }
    },
    
    // Testimonials Section
    testimonials: {
      title: "Khách Hàng Nói Gì",
      subtitle: "Phản hồi từ những khách hàng tôi đã có vinh dự được làm việc cùng",
      cta: {
        title: "Sẵn Sàng Làm Việc Cùng Nhau?",
        desc: "Hãy thảo luận về dự án của bạn và xem tôi có thể giúp biến ý tưởng thành hiện thực như thế nào.",
        button: "Bắt Đầu Dự Án"
      }
    },
    
    // Contact Section
    contact: {
      title: "Liên Hệ",
      subtitle: "Có dự án trong đầu? Hãy thảo luận về cách chúng ta có thể làm việc cùng nhau để biến ý tưởng của bạn thành hiện thực.",
      startConversation: "Bắt Đầu Cuộc Trò Chuyện",
      description: "Tôi luôn quan tâm đến việc nghe về các dự án và cơ hội mới. Dù bạn là công ty muốn tuyển dụng, hay bạn có ý tưởng cần được hiện thực hóa, tôi rất muốn được nghe từ bạn.",
      methods: {
        email: "Email",
        phone: "Điện thoại", 
        location: "Địa điểm"
      },
      followMe: "Theo Dõi Tôi",
      form: {
        name: "Tên của bạn",
        email: "Địa chỉ email",
        subject: "Chủ đề",
        message: "Tin nhắn",
        namePlaceholder: "Nguyễn Văn A",
        emailPlaceholder: "<EMAIL>",
        subjectPlaceholder: "Thảo luận dự án",
        messagePlaceholder: "Hãy kể cho tôi về dự án của bạn...",
        sending: "Đang gửi...",
        send: "Gửi Tin Nhắn",
        success: "Cảm ơn bạn! Tin nhắn đã được gửi thành công."
      }
    },
    
    // Footer
    footer: {
      description: "Fullstack Web Developer đam mê tạo ra những ứng dụng web hiện đại, có thể mở rộng và tạo nên sự khác biệt.",
      quickLinks: "Liên Kết Nhanh",
      getInTouch: "Liên Hệ",
      copyright: "Bản quyền thuộc về"
    }
  },
  
  en: {
    // Navigation
    nav: {
      about: "About",
      projects: "Projects",
      skills: "Tech Stack", 
      contact: "Contact"
    },
    
    // Hero Section
    hero: {
      greeting: "Hi, I'm",
      title: "Fullstack Web Developer",
      tagline: "Helping brands build scalable, modern web applications",
      viewWork: "View My Work",
      getInTouch: "Get In Touch"
    },
    
    // About Section
    about: {
      title: "About Me",
      subtitle: "Passionate about creating digital solutions that make a difference",
      greeting: "Hi, I'm",
      bio: "I'm a 23-year-old fullstack developer with a passion for creating modern, meaningful web applications. With 2+ years of freelance experience, I've helped many businesses and individuals turn their ideas into high-quality technology products.",
      highlights: {
        quality: {
          title: "Focused on Quality",
          desc: "I always prioritize code quality and user experience in every project."
        },
        delivery: {
          title: "Fast Delivery", 
          desc: "Committed to delivering on time with high quality and good communication."
        },
        tech: {
          title: "Modern Tech Stack",
          desc: "Always updating and using the latest technologies to optimize performance."
        },
        collaboration: {
          title: "Client Collaboration",
          desc: "Working closely with clients, listening to feedback and adjusting flexibly."
        }
      },
      stats: {
        age: "Age",
        location: "Location",
        experience: "Experience", 
        projects: "Projects"
      },
      downloadCV: "Download CV",
      sendEmail: "Send Email"
    },
    
    // Projects Section
    projects: {
      title: "Featured Projects",
      subtitle: "A showcase of my recent work and the technologies I've mastered",
      viewDetails: "View Details",
      role: "Role:",
      duration: "Duration:",
      year: "Year:",
      keyFeatures: "Key Features:",
      techUsed: "Technologies Used:",
      status: {
        completed: "Completed",
        inProgress: "In Progress",
        planning: "Planning"
      }
    },
    
    // Skills Section
    skills: {
      title: "Tech Stack & Skills",
      subtitle: "Technologies and tools I use to bring ideas to life",
      categories: {
        frontend: "Frontend",
        backend: "Backend",
        tools: "Tools & Others"
      },
      whatIBring: "What I Bring to Your Project",
      highlights: {
        design: {
          title: "UI/UX Design",
          desc: "Clean, modern interfaces that users love"
        },
        performance: {
          title: "Performance",
          desc: "Fast, optimized applications"
        },
        responsive: {
          title: "Responsive",
          desc: "Perfect on all devices and screens"
        },
        maintenance: {
          title: "Maintenance",
          desc: "Clean code that's easy to maintain"
        }
      }
    },
    
    // Testimonials Section
    testimonials: {
      title: "What Clients Say",
      subtitle: "Feedback from clients I've had the pleasure to work with",
      cta: {
        title: "Ready to Work Together?",
        desc: "Let's discuss your project and see how I can help bring your ideas to life.",
        button: "Start a Project"
      }
    },
    
    // Contact Section
    contact: {
      title: "Get In Touch",
      subtitle: "Have a project in mind? Let's discuss how we can work together to bring your ideas to life.",
      startConversation: "Let's Start a Conversation",
      description: "I'm always interested in hearing about new projects and opportunities. Whether you're a company looking to hire, or you're someone with an idea that needs to be brought to life, I'd love to hear from you.",
      methods: {
        email: "Email",
        phone: "Phone",
        location: "Location"
      },
      followMe: "Follow Me",
      form: {
        name: "Your Name",
        email: "Email Address",
        subject: "Subject",
        message: "Message",
        namePlaceholder: "John Doe",
        emailPlaceholder: "<EMAIL>",
        subjectPlaceholder: "Project Discussion",
        messagePlaceholder: "Tell me about your project...",
        sending: "Sending...",
        send: "Send Message",
        success: "Thank you! Your message has been sent successfully."
      }
    },
    
    // Footer
    footer: {
      description: "Fullstack Web Developer passionate about creating modern, scalable web applications that make a difference.",
      quickLinks: "Quick Links",
      getInTouch: "Get In Touch",
      copyright: "All rights reserved."
    }
  }
};
