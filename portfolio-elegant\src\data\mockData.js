export const projects = [
  {
    id: 1,
    title: {
      vi: "Sell<PERSON><PERSON>",
      en: "SellPro",
    },
    description: {
      vi: "SellPro là một hệ thống quản lý bán hàng toàn di<PERSON>n, cho ph<PERSON>p doanh nghiệp kiểm soát hiệu quả các quy trình như đơn hàng, s<PERSON><PERSON> ph<PERSON>m, tồ<PERSON> kho, công nợ và phân tích hiệu suất kinh doanh. Nền tảng được thiết kế với giao diện hiện đại, tr<PERSON><PERSON> quan, tích hợp biểu đồ thống kê real-time, giúp nhà quản trị ra quyết định kịp thời. Hệ thống hỗ trợ phân quyền người dùng chi tiết và khả năng mở rộng linh hoạt theo quy mô doanh nghiệp.",
      en: "SellPro is a comprehensive sales management platform that enables businesses to effectively control processes such as order handling, product and variant management, inventory, debt tracking, and performance reporting. It features a modern, user-friendly interface with integrated real-time analytics to support fast and informed decision-making. The system includes granular user role permissions and is built to scale with business growth.",
    },
    tech: ["ReactJS", "NodeJS", "MySql", "Redis"],
    image:
      "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=600&h=400&fit=crop",
    role: {
      vi: "Lập trình viên Fullstack",
      en: "Fullstack Developer",
    },
    year: 2025,
    status: "Completed",
    duration: {
      vi: "3 tháng",
      en: "3 months",
    },
    features: {
      vi: [
        "📊 Dashboard phân tích real-time: Hiển thị doanh thu, xu hướng sản phẩm, hiệu suất đơn hàng theo thời gian thực.",
        "📦 Hệ thống quản lý kho: Theo dõi tồn kho theo lô, tự động cập nhật khi có giao dịch bán hoặc nhập hàng.",
        "🛍️ Quản lý sản phẩm & phiên bản sản phẩm: Cho phép tạo, chỉnh sửa sản phẩm và các biến thể như màu sắc, kích thước, SKU.",
        "📈 Báo cáo bán hàng & hiệu suất: Tổng hợp số liệu theo thời gian, theo sản phẩm, theo nhân viên và chi nhánh.",
        "💰 Quản lý công nợ: Theo dõi nợ phải thu của khách hàng và lịch sử thanh toán công nợ theo từng đơn hàng.",
        "🔔 Theo dõi đơn hàng & thông báo: Cập nhật trạng thái đơn hàng theo từng bước, kèm thông báo đến người dùng.",
        "👥 Quản lý nhiều vai trò người dùng: Phân quyền linh hoạt cho quản trị viên, nhân viên, kế toán, v.v.",
      ],
      en: [
        "📊 Real-time analytics dashboard: Displays key business metrics such as revenue, product trends, and order performance in real time.",
        "📦 Inventory management system: Tracks inventory by batches and automatically updates upon sales or restocking.",
        "🛍️ Product & variant management: Create and manage product entries along with variations like color, size, or SKU.",
        "📈 Sales & performance reporting: Aggregates data by time period, product, staff, and store branches for in-depth insights.",
        "💰 Debt management: Tracks receivables from customers and logs debt payment history per order.",
        "🔔 Order tracking & notifications: Updates users on each processing stage of orders with notification system.",
        "👥 Multi-user role management: Flexible role-based access for admin, staff, accountants, and more.",
      ],
    },
  },
  {
    id: 2,
    title: {
      vi: "Landing Page sự kiện TEDx",
      en: "TEDx Event Landing Page",
    },
    description: {
      vi: "Thiết kế giao diện đẹp, responsive cho một sự kiện TEDx. Tối ưu SEO và tốc độ loading, tích hợp form đăng ký và countdown timer.",
      en: "Beautiful, responsive interface design for a TEDx event. SEO optimized with fast loading speed, integrated registration form and countdown timer.",
    },
    tech: ["VueJS", "TailwindCSS", "GSAP"],
    image:
      "https://images.unsplash.com/photo-*************-178a50c2df87?w=600&h=400&fit=crop",
    role: {
      vi: "Frontend Developer",
      en: "Frontend Developer",
    },
    year: 2023,
    status: "Completed",
    duration: {
      vi: "1 tháng",
      en: "1 month",
    },
    features: {
      vi: [
        "Responsive design cho mọi thiết bị",
        "Smooth animations với GSAP",
        "SEO optimization",
        "Event countdown timer",
      ],
      en: [
        "Responsive design for all devices",
        "Smooth animations with GSAP",
        "SEO optimization",
        "Event countdown timer",
      ],
    },
  },
  {
    id: 3,
    title: {
      vi: "Hệ thống booking khám bệnh",
      en: "Medical Booking System",
    },
    description: {
      vi: "Web đặt lịch và quản lý hồ sơ bệnh nhân, có phần quản trị riêng. Tích hợp thanh toán online và gửi SMS nhắc lịch hẹn.",
      en: "Web application for appointment booking and patient record management with separate admin panel. Integrated online payment and SMS appointment reminders.",
    },
    tech: [".NET Core", "SQL Server", "Bootstrap", "SignalR"],
    image:
      "https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=600&h=400&fit=crop",
    role: {
      vi: "Backend Developer",
      en: "Backend Developer",
    },
    year: 2024,
    status: "Completed",
    duration: {
      vi: "4 tháng",
      en: "4 months",
    },
    features: {
      vi: [
        "Đặt lịch hẹn online",
        "Quản lý hồ sơ bệnh nhân",
        "Tích hợp cổng thanh toán",
        "Hệ thống thông báo SMS",
      ],
      en: [
        "Online appointment booking",
        "Patient record management",
        "Payment gateway integration",
        "SMS notification system",
      ],
    },
  },
  {
    id: 4,
    title: {
      vi: "Website portfolio cho designer",
      en: "Designer Portfolio Website",
    },
    description: {
      vi: "Website giới thiệu cá nhân, gallery ảnh, portfolio. Thiết kế tối giản, tập trung vào việc showcase các tác phẩm một cách ấn tượng.",
      en: "Personal introduction website with photo gallery and portfolio. Minimalist design focused on showcasing works impressively.",
    },
    tech: ["Laravel", "PHP", "MySQL", "Alpine.js"],
    image:
      "https://images.unsplash.com/photo-1467232004584-a241de8bcf5d?w=600&h=400&fit=crop",
    role: {
      vi: "Fullstack Developer",
      en: "Fullstack Developer",
    },
    year: 2023,
    status: "Completed",
    duration: {
      vi: "2 tháng",
      en: "2 months",
    },
    features: {
      vi: [
        "Gallery portfolio động",
        "Admin panel quản lý nội dung",
        "Form liên hệ tích hợp email",
        "Cấu trúc thân thiện SEO",
      ],
      en: [
        "Dynamic portfolio gallery",
        "Admin panel for content management",
        "Contact form with email integration",
        "SEO-friendly structure",
      ],
    },
  },
];

export const skills = [
  {
    category: "Frontend",
    technologies: [
      { name: "ReactJS", icon: "⚛️" },
      { name: "VueJS", icon: "💚" },
      { name: "NextJS", icon: "▲" },
      { name: "TailwindCSS", icon: "🎨" },
      { name: "TypeScript", icon: "📘" },
      { name: "JavaScript", icon: "🟨" },
    ],
  },
  {
    category: "Backend",
    technologies: [
      { name: "NodeJS", icon: "🟢" },
      { name: "Laravel", icon: "🔴" },
      { name: "PHP", icon: "🐘" },
      { name: ".NET Core", icon: "🔷" },
      { name: "MySQL", icon: "🐬" },
      { name: "MongoDB", icon: "🍃" },
    ],
  },
  {
    category: "Tools",
    technologies: [
      { name: "Git", icon: "📚" },
      { name: "Docker", icon: "🐳" },
      { name: "AWS", icon: "☁️" },
      { name: "Figma", icon: "🎨" },
      { name: "VS Code", icon: "💙" },
      { name: "Postman", icon: "🧡" },
    ],
  },
];

export const testimonials = [
  {
    id: 1,
    name: {
      vi: "Anh Minh Tuấn",
      en: "Mr. Minh Tuan",
    },
    position: {
      vi: "CEO, TechStart Vietnam",
      en: "CEO, TechStart Vietnam",
    },
    content: {
      vi: "Làm việc với bạn ấy rất chuyên nghiệp. Giao tiếp tốt, deadline đúng hẹn và chất lượng code rất clean.",
      en: "Working with him is very professional. Good communication, on-time delivery and very clean code quality.",
    },
    avatar:
      "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",
    rating: 5,
  },
  {
    id: 2,
    name: {
      vi: "Chị Lan Anh",
      en: "Ms. Lan Anh",
    },
    position: {
      vi: "Product Manager, Digital Agency",
      en: "Product Manager, Digital Agency",
    },
    content: {
      vi: "Dự án được hoàn thành vượt mong đợi. Giao diện đẹp, responsive tốt và có nhiều tính năng hay ho.",
      en: "The project was completed beyond expectations. Beautiful interface, good responsive design and many interesting features.",
    },
    avatar:
      "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face",
    rating: 5,
  },
];

export const personalInfo = {
  name: "Nguyễn Hải Dương",
  age: 22,
  title: {
    vi: "Fullstack Web Developer",
    en: "Fullstack Web Developer",
  },
  tagline: {
    vi: "Giúp các thương hiệu xây dựng ứng dụng web hiện đại và có thể mở rộng",
    en: "Helping brands build scalable, modern web applications",
  },
  email: "<EMAIL>",
  phone: "0367805247",
  location: {
    vi: "Phường Nghĩa Đô, Hà Nội",
    en: "Nghia Do Ward, Hanoi",
  },
  avatar:
    "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face",
  bio: {
    vi: "Tôi là một lập trình viên fullstack 22 tuổi với niềm đam mê tạo ra những ứng dụng web hiện đại và có ý nghĩa. Với 2+ năm kinh nghiệm freelance, tôi đã giúp nhiều doanh nghiệp và cá nhân hiện thực hóa ý tưởng của họ thành những sản phẩm công nghệ chất lượng cao.",
    en: "I'm a 22-year-old fullstack developer with a passion for creating modern, meaningful web applications. With 2+ years of freelance experience, I've helped many businesses and individuals turn their ideas into high-quality technology products.",
  },
  socialLinks: {
    github: "https://github.com/duongdev",
    linkedin: "https://linkedin.com/in/duongdev",
    facebook: "https://facebook.com/duongdev",
    instagram: "https://instagram.com/duongdev",
  },
  resume: {
    vi: "/resume-duong-developer-vi.pdf",
    en: "/resume-duong-developer-en.pdf",
  },
};
