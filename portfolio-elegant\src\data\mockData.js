export const projects = [
  {
    id: 1,
    title: {
      vi: "E-commerce Dashboard",
      en: "E-commerce Dashboard"
    },
    description: {
      vi: "Trang quản trị đơn hàng và sản phẩm cho hệ thống bán hàng. Giao diện hiện đại với biểu đồ thống kê real-time và quản lý inventory thông minh.",
      en: "Order and product management dashboard for e-commerce system. Modern interface with real-time analytics charts and smart inventory management."
    },
    tech: ["ReactJS", "NodeJS", "MongoDB", "Chart.js"],
    image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=600&h=400&fit=crop",
    role: {
      vi: "Fullstack Developer",
      en: "Fullstack Developer"
    },
    year: 2024,
    status: "Completed",
    duration: {
      vi: "3 tháng",
      en: "3 months"
    },
    features: {
      vi: [
        "Dashboard phân tích real-time",
        "<PERSON><PERSON> thống quản lý kho",
        "<PERSON> dõi đơn hàng & thông báo",
        "Quản lý nhiều vai trò người dùng"
      ],
      en: [
        "Real-time analytics dashboard",
        "Inventory management system",
        "Order tracking & notifications",
        "Multi-user role management"
      ]
    }
  },
  {
    id: 2,
    title: {
      vi: "Landing Page sự kiện TEDx",
      en: "TEDx Event Landing Page"
    },
    description: {
      vi: "Thiết kế giao diện đẹp, responsive cho một sự kiện TEDx. Tối ưu SEO và tốc độ loading, tích hợp form đăng ký và countdown timer.",
      en: "Beautiful, responsive interface design for a TEDx event. SEO optimized with fast loading speed, integrated registration form and countdown timer."
    },
    tech: ["VueJS", "TailwindCSS", "GSAP"],
    image: "https://images.unsplash.com/photo-1540575467063-178a50c2df87?w=600&h=400&fit=crop",
    role: {
      vi: "Frontend Developer",
      en: "Frontend Developer"
    },
    year: 2023,
    status: "Completed",
    duration: {
      vi: "1 tháng",
      en: "1 month"
    },
    features: {
      vi: [
        "Responsive design cho mọi thiết bị",
        "Smooth animations với GSAP",
        "SEO optimization",
        "Event countdown timer"
      ],
      en: [
        "Responsive design for all devices",
        "Smooth animations with GSAP",
        "SEO optimization",
        "Event countdown timer"
      ]
    }
  },
  {
    id: 3,
    title: {
      vi: "Hệ thống booking khám bệnh",
      en: "Medical Booking System"
    },
    description: {
      vi: "Web đặt lịch và quản lý hồ sơ bệnh nhân, có phần quản trị riêng. Tích hợp thanh toán online và gửi SMS nhắc lịch hẹn.",
      en: "Web application for appointment booking and patient record management with separate admin panel. Integrated online payment and SMS appointment reminders."
    },
    tech: [".NET Core", "SQL Server", "Bootstrap", "SignalR"],
    image: "https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=600&h=400&fit=crop",
    role: {
      vi: "Backend Developer",
      en: "Backend Developer"
    },
    year: 2024,
    status: "Completed",
    duration: {
      vi: "4 tháng",
      en: "4 months"
    },
    features: {
      vi: [
        "Đặt lịch hẹn online",
        "Quản lý hồ sơ bệnh nhân",
        "Tích hợp cổng thanh toán",
        "Hệ thống thông báo SMS"
      ],
      en: [
        "Online appointment booking",
        "Patient record management",
        "Payment gateway integration",
        "SMS notification system"
      ]
    }
  },
  {
    id: 4,
    title: {
      vi: "Website portfolio cho designer",
      en: "Designer Portfolio Website"
    },
    description: {
      vi: "Website giới thiệu cá nhân, gallery ảnh, portfolio. Thiết kế tối giản, tập trung vào việc showcase các tác phẩm một cách ấn tượng.",
      en: "Personal introduction website with photo gallery and portfolio. Minimalist design focused on showcasing works impressively."
    },
    tech: ["Laravel", "PHP", "MySQL", "Alpine.js"],
    image: "https://images.unsplash.com/photo-1467232004584-a241de8bcf5d?w=600&h=400&fit=crop",
    role: {
      vi: "Fullstack Developer",
      en: "Fullstack Developer"
    },
    year: 2023,
    status: "Completed",
    duration: {
      vi: "2 tháng",
      en: "2 months"
    },
    features: {
      vi: [
        "Gallery portfolio động",
        "Admin panel quản lý nội dung",
        "Form liên hệ tích hợp email",
        "Cấu trúc thân thiện SEO"
      ],
      en: [
        "Dynamic portfolio gallery",
        "Admin panel for content management",
        "Contact form with email integration",
        "SEO-friendly structure"
      ]
    }
  }
];

export const skills = [
  {
    category: "Frontend",
    technologies: [
      { name: "ReactJS", level: 90, icon: "⚛️" },
      { name: "VueJS", level: 85, icon: "💚" },
      { name: "NextJS", level: 80, icon: "▲" },
      { name: "TailwindCSS", level: 95, icon: "🎨" },
      { name: "TypeScript", level: 75, icon: "📘" }
    ]
  },
  {
    category: "Backend", 
    technologies: [
      { name: "NodeJS", level: 85, icon: "🟢" },
      { name: "Laravel", level: 80, icon: "🔴" },
      { name: "PHP", level: 85, icon: "🐘" },
      { name: ".NET Core", level: 75, icon: "🔷" },
      { name: "MySQL", level: 80, icon: "🐬" }
    ]
  },
  {
    category: "Tools & Others",
    technologies: [
      { name: "Git", level: 90, icon: "📚" },
      { name: "Docker", level: 70, icon: "🐳" },
      { name: "AWS", level: 65, icon: "☁️" },
      { name: "Figma", level: 80, icon: "🎨" }
    ]
  }
];

export const testimonials = [
  {
    id: 1,
    name: {
      vi: "Anh Minh Tuấn",
      en: "Mr. Minh Tuan"
    },
    position: {
      vi: "CEO, TechStart Vietnam",
      en: "CEO, TechStart Vietnam"
    },
    content: {
      vi: "Làm việc với bạn ấy rất chuyên nghiệp. Giao tiếp tốt, deadline đúng hẹn và chất lượng code rất clean.",
      en: "Working with him is very professional. Good communication, on-time delivery and very clean code quality."
    },
    avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",
    rating: 5
  },
  {
    id: 2,
    name: {
      vi: "Chị Lan Anh",
      en: "Ms. Lan Anh"
    },
    position: {
      vi: "Product Manager, Digital Agency",
      en: "Product Manager, Digital Agency"
    },
    content: {
      vi: "Dự án được hoàn thành vượt mong đợi. Giao diện đẹp, responsive tốt và có nhiều tính năng hay ho.",
      en: "The project was completed beyond expectations. Beautiful interface, good responsive design and many interesting features."
    },
    avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face",
    rating: 5
  }
];

export const personalInfo = {
  name: "Nguyễn Văn Nam",
  age: 23,
  title: {
    vi: "Fullstack Web Developer",
    en: "Fullstack Web Developer"
  },
  tagline: {
    vi: "Giúp các thương hiệu xây dựng ứng dụng web hiện đại và có thể mở rộng",
    en: "Helping brands build scalable, modern web applications"
  },
  email: "<EMAIL>",
  phone: "+84 123 456 789",
  location: {
    vi: "TP. Hồ Chí Minh, Việt Nam",
    en: "Ho Chi Minh City, Vietnam"
  },
  avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face",
  bio: {
    vi: "Tôi là một lập trình viên fullstack 23 tuổi với niềm đam mê tạo ra những ứng dụng web hiện đại và có ý nghĩa. Với 2+ năm kinh nghiệm freelance, tôi đã giúp nhiều doanh nghiệp và cá nhân hiện thực hóa ý tưởng của họ thành những sản phẩm công nghệ chất lượng cao.",
    en: "I'm a 23-year-old fullstack developer with a passion for creating modern, meaningful web applications. With 2+ years of freelance experience, I've helped many businesses and individuals turn their ideas into high-quality technology products."
  },
  socialLinks: {
    github: "https://github.com/namdev",
    linkedin: "https://linkedin.com/in/namdev",
    facebook: "https://facebook.com/namdev",
    instagram: "https://instagram.com/namdev"
  },
  resume: {
    vi: "/resume-nam-developer-vi.pdf",
    en: "/resume-nam-developer-en.pdf"
  }
};
