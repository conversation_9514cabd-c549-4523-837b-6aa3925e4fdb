import { personalInfo } from '../data/mockData';

const About = () => {
  const highlights = [
    {
      icon: "🎯",
      title: "Focused on Quality",
      description: "Tôi luôn đặt chất lượng code và user experience lên hàng đầu trong mọi dự án."
    },
    {
      icon: "⚡",
      title: "Fast Delivery",
      description: "Cam kết giao hàng đúng deadline với chất lượng cao và communication tốt."
    },
    {
      icon: "🚀",
      title: "Modern Tech Stack",
      description: "Luôn cập nhật và sử dụng những công nghệ mới nhất để tối ưu hiệu suất."
    },
    {
      icon: "🤝",
      title: "Client Collaboration",
      description: "Làm việc chặt chẽ với khách hàng, lắng nghe feedback và điều chỉnh linh hoạt."
    }
  ];

  return (
    <section id="about" className="section-padding bg-white">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold text-slate-900 mb-4">
            About Me
          </h2>
          <p className="text-lg text-slate-600 max-w-2xl mx-auto">
            Passionate about creating digital solutions that make a difference
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left side - Text content */}
          <div className="space-y-6">
            <div className="space-y-4">
              <h3 className="text-2xl font-semibold text-slate-800">
                Hi, I'm {personalInfo.name}
              </h3>
              <p className="text-slate-600 leading-relaxed">
                {personalInfo.bio}
              </p>
            </div>

            <div className="grid grid-cols-2 gap-4 py-6">
              <div>
                <h4 className="font-semibold text-slate-800 mb-2">Age</h4>
                <p className="text-slate-600">{personalInfo.age} years old</p>
              </div>
              <div>
                <h4 className="font-semibold text-slate-800 mb-2">Location</h4>
                <p className="text-slate-600">{personalInfo.location}</p>
              </div>
              <div>
                <h4 className="font-semibold text-slate-800 mb-2">Experience</h4>
                <p className="text-slate-600">2+ years</p>
              </div>
              <div>
                <h4 className="font-semibold text-slate-800 mb-2">Projects</h4>
                <p className="text-slate-600">15+ completed</p>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <a 
                href={personalInfo.resume}
                download
                className="btn-primary inline-flex items-center justify-center"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-4-4m4 4l4-4m-4-4V3" />
                </svg>
                Download CV
              </a>
              <a 
                href={`mailto:${personalInfo.email}`}
                className="btn-secondary inline-flex items-center justify-center"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                Send Email
              </a>
            </div>
          </div>

          {/* Right side - Highlights */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            {highlights.map((highlight, index) => (
              <div 
                key={index}
                className="card text-center hover:scale-105 transition-transform duration-300"
              >
                <div className="text-4xl mb-4">{highlight.icon}</div>
                <h4 className="text-lg font-semibold text-slate-800 mb-2">
                  {highlight.title}
                </h4>
                <p className="text-slate-600 text-sm leading-relaxed">
                  {highlight.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
