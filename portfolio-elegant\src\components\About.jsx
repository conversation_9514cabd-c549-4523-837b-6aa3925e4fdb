import { personalInfo } from '../data/mockData';
import { useLanguage } from '../contexts/LanguageContext';
import { translations } from '../data/translations';

const About = () => {
  const { language } = useLanguage();
  const t = translations[language];

  const highlights = [
    {
      icon: "🎯",
      title: t.about.highlights.quality.title,
      description: t.about.highlights.quality.desc
    },
    {
      icon: "⚡",
      title: t.about.highlights.delivery.title,
      description: t.about.highlights.delivery.desc
    },
    {
      icon: "🚀",
      title: t.about.highlights.tech.title,
      description: t.about.highlights.tech.desc
    },
    {
      icon: "🤝",
      title: t.about.highlights.collaboration.title,
      description: t.about.highlights.collaboration.desc
    }
  ];

  return (
    <section id="about" className="section-padding bg-white">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold text-slate-900 mb-4">
            {t.about.title}
          </h2>
          <p className="text-lg text-slate-600 max-w-2xl mx-auto">
            {t.about.subtitle}
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left side - Text content */}
          <div className="space-y-6">
            <div className="space-y-4">
              <h3 className="text-2xl font-semibold text-slate-800">
                {t.about.greeting} {personalInfo.name}
              </h3>
              <p className="text-slate-600 leading-relaxed">
                {typeof personalInfo.bio === 'object' ? personalInfo.bio[language] : personalInfo.bio}
              </p>
            </div>

            <div className="grid grid-cols-2 gap-4 py-6">
              <div>
                <h4 className="font-semibold text-slate-800 mb-2">{t.about.stats.age}</h4>
                <p className="text-slate-600">{personalInfo.age} {language === 'vi' ? 'tuổi' : 'years old'}</p>
              </div>
              <div>
                <h4 className="font-semibold text-slate-800 mb-2">{t.about.stats.location}</h4>
                <p className="text-slate-600">{typeof personalInfo.location === 'object' ? personalInfo.location[language] : personalInfo.location}</p>
              </div>
              <div>
                <h4 className="font-semibold text-slate-800 mb-2">{t.about.stats.experience}</h4>
                <p className="text-slate-600">2+ {language === 'vi' ? 'năm' : 'years'}</p>
              </div>
              <div>
                <h4 className="font-semibold text-slate-800 mb-2">{t.about.stats.projects}</h4>
                <p className="text-slate-600">15+ {language === 'vi' ? 'hoàn thành' : 'completed'}</p>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <a
                href={typeof personalInfo.resume === 'object' ? personalInfo.resume[language] : personalInfo.resume}
                download
                className="btn-primary inline-flex items-center justify-center"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-4-4m4 4l4-4m-4-4V3" />
                </svg>
                {t.about.downloadCV}
              </a>
              <a
                href={`mailto:${personalInfo.email}`}
                className="btn-secondary inline-flex items-center justify-center"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                {t.about.sendEmail}
              </a>
            </div>
          </div>

          {/* Right side - Highlights */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            {highlights.map((highlight, index) => (
              <div 
                key={index}
                className="card text-center hover:scale-105 transition-transform duration-300"
              >
                <div className="text-4xl mb-4">{highlight.icon}</div>
                <h4 className="text-lg font-semibold text-slate-800 mb-2">
                  {highlight.title}
                </h4>
                <p className="text-slate-600 text-sm leading-relaxed">
                  {highlight.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
