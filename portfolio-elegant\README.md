# 🎨 Portfolio Elegant & Professional

Một trang portfolio chuyên nghiệp và tinh tế được xây dựng bằng ReactJS và TailwindCSS, dành cho freelancer và developer muốn gây ấn tượng với khách hàng và nhà tuyển dụng.

## ✨ Tính năng nổi bật

- 🎯 **Thiết kế chuyên nghiệp**: Tone elegant với màu sắc navy-white-gray
- 📱 **Responsive hoàn toàn**: Tối ưu cho mọi thiết bị
- ⚡ **Performance cao**: Sử dụng Vite và TailwindCSS
- 🔧 **Dễ tùy chỉnh**: Mock data và cấu hình linh hoạt
- 🎨 **Animations mượt mà**: Hover effects và smooth scrolling

## 🚀 Tech Stack

- **Frontend**: ReactJS 18 + Vite
- **Styling**: TailwindCSS
- **Icons**: Heroicons (SVG)
- **Fonts**: Inter (Google Fonts)
- **Build Tool**: Vite
- **Package Manager**: npm

## 🛠️ Cài đặt và chạy

### Prerequisites
- Node.js (phiên bản 16+ khuyến nghị)
- npm hoặc yarn

### Cài đặt
```bash
# Cài đặt dependencies
npm install

# Chạy development server
npm run dev

# Build cho production
npm run build

# Preview production build
npm run preview
```

## 🎨 Tùy chỉnh

### 1. Thông tin cá nhân
Chỉnh sửa file `src/data/mockData.js`:

```javascript
export const personalInfo = {
  name: "Tên của bạn",
  age: 25,
  title: "Fullstack Developer",
  tagline: "Slogan của bạn",
  email: "<EMAIL>",
  // ... các thông tin khác
}
```

### 2. Dự án
Thêm/sửa dự án trong `projects` array:

```javascript
export const projects = [
  {
    id: 1,
    title: "Tên dự án",
    description: "Mô tả dự án...",
    tech: ["ReactJS", "NodeJS"],
    image: "link-to-image",
    // ... thông tin khác
  }
]
```

### 3. Skills & Màu sắc
- Cập nhật skills trong `skills` array
- Chỉnh sửa `tailwind.config.js` để thay đổi color scheme

## 📱 Sections

1. **Header**: Navigation với logo và menu responsive
2. **Hero**: Giới thiệu bản thân với CTA buttons
3. **About**: Thông tin chi tiết và highlights
4. **Projects**: Showcase dự án với modal chi tiết
5. **Skills**: Tech stack với progress bars
6. **Testimonials**: Feedback từ khách hàng
7. **Contact**: Form liên hệ và thông tin contact
8. **Footer**: Links và thông tin bổ sung

## 🎯 Tối ưu SEO

- Meta tags đầy đủ
- Open Graph tags
- Twitter Card tags
- Semantic HTML
- Alt text cho images

## 📦 Deploy

### Vercel
```bash
npm install -g vercel
vercel
```

### Netlify
```bash
npm run build
# Upload dist folder to Netlify
```

---

⭐ Nếu project này hữu ích, hãy cho một star nhé! ⭐
